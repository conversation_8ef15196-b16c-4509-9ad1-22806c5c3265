import { CrmEmpresa, Socio } from './CrmEmpresa';
import { LeadLink, TipoLinkLead } from './LeadLink';
import { CrmTelefoneLead, TipoTelefoneLead } from './CrmTelefoneLead';
import { EtapaFunilLead, OrigemLead, SegmentoLead, ConcorrenteLead } from './LeadEnums';
import { InstagramData, RapportGerado } from './LeadInterfaces';

export class Lead {
  id?: number;
  crmEmpresaId: number = 0; // Vinculação obrigatória com CrmEmpresa
  nomeResponsavel: string = '';
  empresa: string = '';
  cnpj?: string; // CNPJ da empresa (apenas números)
  cidade?: string; // Cidade da empresa
  endereco?: string; // Endereço completo da empresa
  telefone: string = '';
  instagramHandle?: string;
  linkInsta?: string; // URL/link externo da bio do Instagram
  // Bio extraída diretamente do perfil do Instagram (campo duplicado para fácil acesso)
  bioInsta?: string;

  etapa: EtapaFunilLead = EtapaFunilLead.Prospecção;
  score = 0; // 0-100
  origem: OrigemLead = OrigemLead.Instagram;
  segmento?: SegmentoLead;
  concorrente?: ConcorrenteLead;

  // Datas
  dataCriacao: Date = new Date();
  dataUltimaInteracao?: Date;
  dataProximoFollowup?: Date;
  dataFechamento?: Date;

  // Dados de negócio
  valorPotencial?: number;
  vendedorId?: number;
  motivoPerda?: string;

  // Dados do Instagram (quando origem = Instagram)
  instagramData?: InstagramData;
  avatarUrl?: string; // URL da foto de perfil do Instagram

  // Website da empresa (compatibilidade com frontend)
  website?: string;

  // Dados gerados por IA
  rapport?: RapportGerado;
  relatorioIaJson?: string; // JSON com dados completos da análise de IA
  faseSpinDetectada?: string; // Fase SPIN detectada automaticamente pela IA
  confiancaFaseDetectada?: number; // Confiança da detecção (0-1)
  dataUltimaDeteccaoFase?: Date; // Quando foi feita a última detecção

  // Observações livres
  notas?: string;
  observacoes?: string; // Observações específicas sobre vendas/negociação

  // Links do lead
  links?: LeadLink[];

  // Telefones do lead
  telefones?: CrmTelefoneLead[];

  // Campos de auditoria
  createdAt?: Date;
  updatedAt?: Date;

  crmEmpresa?: CrmEmpresa;

  constructor() {
    // Construtor sem parâmetros - propriedades devem ser definidas após a criação
  }

  // Método auxiliar para configurar dados básicos do lead
  configurarDadosBasicos(
    crmEmpresaId: number,
    nomeResponsavel: string,
    empresa: string,
    telefone: string,
    instagramHandle?: string,
    bioInsta?: string,
    origem: OrigemLead = OrigemLead.Instagram,
    endereco?: string,
    cnpj?: string
  ): void {
    this.crmEmpresaId = crmEmpresaId;
    this.nomeResponsavel = nomeResponsavel?.trim() || '';
    this.empresa = empresa?.trim() || '';
    this.telefone = telefone?.replace(/\D/g, '') || '';
    this.instagramHandle = instagramHandle?.replace('@', '').trim();
    if (bioInsta) this.bioInsta = bioInsta;
    if (endereco) this.endereco = endereco?.trim();
    if (cnpj) this.cnpj = cnpj.replace(/\D/g, ''); // Manter apenas números
    this.origem = origem;
  }

  // Métodos de negócio
  avancarEtapa(): boolean {
    const ordem: EtapaFunilLead[] = [
      EtapaFunilLead.Prospecção,
      EtapaFunilLead.Qualificação,
      EtapaFunilLead.Objeção,
      EtapaFunilLead.Fechamento,
      EtapaFunilLead.Ganho
    ];
    const idx = ordem.indexOf(this.etapa);
    if (idx >= 0 && idx < ordem.length - 1) {
      this.etapa = ordem[idx + 1];
      this.dataUltimaInteracao = new Date();
      return true;
    }
    return false;
  }

  retrocederEtapa(): boolean {
    const ordem: EtapaFunilLead[] = [
      EtapaFunilLead.Prospecção,
      EtapaFunilLead.Qualificação,
      EtapaFunilLead.Objeção,
      EtapaFunilLead.Fechamento,
      EtapaFunilLead.Ganho
    ];
    const idx = ordem.indexOf(this.etapa);
    if (idx > 0) {
      this.etapa = ordem[idx - 1];
      this.dataUltimaInteracao = new Date();
      return true;
    }
    return false;
  }

  marcarComoGanho(valorFechamento?: number): void {
    this.etapa = EtapaFunilLead.Ganho;
    this.dataFechamento = new Date();
    this.dataUltimaInteracao = new Date();
    if (valorFechamento) {
      this.valorPotencial = valorFechamento;
    }
    this.score = 100;
  }

  marcarComoPerdido(motivo?: string): void {
    this.etapa = EtapaFunilLead.Perdido;
    this.dataFechamento = new Date();
    this.dataUltimaInteracao = new Date();
    this.motivoPerda = motivo;
    this.score = 0;
  }

  // Classificações
  getScoreClassificacao(): 'Alto' | 'Médio' | 'Baixo' | 'Sem' {
    if (this.score >= 80) return 'Alto';
    if (this.score >= 50) return 'Médio';
    if (this.score > 0) return 'Baixo';
    return 'Sem';
  }

  getCorScore(): string {
    if (this.score >= 80) return '#28a745'; // Verde
    if (this.score >= 50) return '#ffc107'; // Amarelo
    if (this.score > 0) return '#dc3545';   // Vermelho
    return '#6c757d'; // Cinza
  }

  getIconeEtapa(): string {
    const icones = {
      'Prospecção': 'fa-eye',
      'Qualificação': 'fa-search',
      'Objeção': 'fa-exclamation-triangle',
      'Fechamento': 'fa-handshake',
      'Ganho': 'fa-check-circle',
      'Perdido': 'fa-times-circle'
    };
    return icones[this.etapa] || 'fa-circle';
  }

  // Validações
  isValid(): boolean {
    return !!(this.crmEmpresaId &&
             this.nomeResponsavel?.trim() &&
             this.empresa?.trim() &&
             this.telefone?.trim());
  }

  isFechado(): boolean {
    return this.etapa === EtapaFunilLead.Ganho || this.etapa === EtapaFunilLead.Perdido;
  }

  isAtrasado(): boolean {
    if (!this.dataProximoFollowup) return false;
    return new Date() > this.dataProximoFollowup;
  }

  // Formatações
  formatarTelefone(): string {
    if (!this.telefone) return '';
    const telefone = this.telefone.replace(/\D/g, '');
    if (telefone.length === 11) {
      return telefone.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (telefone.length === 10) {
      return telefone.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return this.telefone;
  }

  formatarCnpj(): string {
    if (!this.cnpj) return '';
    const cnpj = this.cnpj.replace(/\D/g, '');
    if (cnpj.length === 14) {
      return cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    }
    return this.cnpj;
  }

  getCnpjLimpo(): string {
    return this.cnpj?.replace(/\D/g, '') || '';
  }

  isValidCnpj(): boolean {
    if (!this.cnpj) return false;
    const cnpjLimpo = this.cnpj.replace(/\D/g, '');
    return cnpjLimpo.length === 14;
  }

  formatarInstagram(): string {
    if (!this.instagramHandle) return '';
    return `@${this.instagramHandle}`;
  }

  getLinkBioInstagram(): string {
    return this.linkInsta || '';
  }

  getLinkPerfilInstagram(): string {
    if (this.instagramHandle) return `https://instagram.com/${this.instagramHandle}`;
    return '';
  }

  formatarValorPotencial(): string {
    if (!this.valorPotencial) return 'N/A';
    return this.valorPotencial.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    });
  }

  // Resumo para listagens
  getResumo(): string {
    return `${this.nomeResponsavel} - ${this.empresa} (${this.etapa})`;
  }

  // Formatação de endereço
  getEnderecoFormatado(): string {
    if (!this.endereco) return '';
    return this.endereco;
  }

  // Endereço completo com cidade
  getEnderecoCompleto(): string {
    const partes = [];
    if (this.endereco) partes.push(this.endereco);
    if (this.cidade) partes.push(this.cidade);
    return partes.join(', ');
  }

  // Validação de endereço
  hasEndereco(): boolean {
    return !!(this.endereco?.trim());
  }

  // Métodos para extrair links específicos por tipo
  getLinkPorTipo(tipo: string): string | null {
    if (!this.links || this.links.length === 0) return null;
    const link = this.links.find(l => l.tipo === tipo && l.ativo);
    return link ? link.url : null;
  }

  // Obter objeto LeadLink por tipo
  getLeadLinkPorTipo(tipo: TipoLinkLead): LeadLink | undefined {
    return this.links?.find(link => link.tipo === tipo && link.ativo);
  }

  getLinkInstagram(): string | null {
    return this.getLinkPorTipo('Instagram') || this.linkInsta || this.getLinkPerfilInstagram();
  }

  getLinkSite(): string | null {
    return this.getLinkPorTipo('Site') || this.website || this.instagramData?.website;
  }

  getLinkConcorrente(): string | null {
    return this.getLinkPorTipo('Concorrente');
  }

  getLinkIfood(): string | null {
    return this.getLinkPorTipo('Ifood');
  }

  getLinkWhatsApp(): string | null {
    return this.getLinkPorTipo('WhatsApp');
  }

  getLinkLocalizacao(): string | null {
    return this.getLinkPorTipo('Localização');
  }

  // Obter todas as URLs dos links ativos para envio ao Bitrix
  getAllLinksUrls(): string[] {
    if (!this.links || this.links.length === 0) return [];

    return this.links
      .filter(link => link.ativo)
      .map(link => link.getUrlFormatada())
      .filter(url => url && url.trim() !== '');
  }

  // ===== MÉTODOS PARA GERENCIAR LINKS =====

  // Adicionar um novo link
  adicionarLink(tipo: TipoLinkLead, url: string, descricao?: string): LeadLink {
    if (!this.links) this.links = [];

    // Verificar se já existe um link do mesmo tipo
    const linkExistente = this.links.find(link => link.tipo === tipo);
    if (linkExistente) {
      linkExistente.url = url;
      linkExistente.descricao = descricao;
      linkExistente.ativo = true;
      return linkExistente;
    }

    const novoLink = new LeadLink(this.id || 0, tipo, url, descricao, this.links.length);
    this.links.push(novoLink);
    this.sincronizarLinkInsta();
    return novoLink;
  }

  // Remover link por tipo
  removerLink(tipo: TipoLinkLead): boolean {
    if (!this.links) return false;

    const index = this.links.findIndex(link => link.tipo === tipo);
    if (index >= 0) {
      this.links.splice(index, 1);
      this.sincronizarLinkInsta();
      return true;
    }
    return false;
  }



  // Obter todos os links ativos
  getLinksAtivos(): LeadLink[] {
    return this.links?.filter(link => link.ativo) || [];
  }

  // Obter links por categoria
  getLinksPorCategoria(): { [categoria: string]: LeadLink[] } {
    const links = this.getLinksAtivos();
    return {
      'Contato': links.filter(l => ['WhatsApp', 'Instagram'].includes(l.tipo)),
      'Negócio': links.filter(l => ['Ifood', 'Site do Cardápio', 'Reservas'].includes(l.tipo)),
      'Informações': links.filter(l => ['Site', 'Localização'].includes(l.tipo))
    };
  }

  // Sincronizar campo linkInsta com array de links (compatibilidade)
  sincronizarLinkInsta(): void {
    const linkInstagram = this.getLeadLinkPorTipo(TipoLinkLead.Instagram);
    this.linkInsta = linkInstagram?.url || '';
  }

  // Inicializar links a partir do campo linkInsta existente
  inicializarLinksDoLinkInsta(): void {
    if (this.linkInsta && !this.getLeadLinkPorTipo(TipoLinkLead.Instagram)) {
      this.adicionarLink(TipoLinkLead.Instagram, this.linkInsta, 'Link da bio do Instagram');
    }
  }

  // Validar todos os links
  validarLinks(): { validos: LeadLink[], invalidos: LeadLink[] } {
    const links = this.getLinksAtivos();
    return {
      validos: links.filter(link => link.isUrlValida()),
      invalidos: links.filter(link => !link.isUrlValida())
    };
  }

  // Obter link do WhatsApp formatado
  getWhatsAppUrl(): string {
    const linkWhatsApp = this.getLeadLinkPorTipo(TipoLinkLead.Whatsapp);
    if (linkWhatsApp) return linkWhatsApp.getUrlFormatada();

    // Fallback para o telefone
    if (this.telefone) {
      const numeroLimpo = this.telefone.replace(/\D/g, '');
      return `https://wa.me/55${numeroLimpo}`;
    }
    return '';
  }

  // Obter link de localização
  getLocalizacaoUrl(): string {
    const linkLocalizacao = this.getLeadLinkPorTipo(TipoLinkLead.Localizacao);
    return linkLocalizacao?.getUrlFormatada() || '';
  }

  // ===== MÉTODOS PARA GERENCIAR TELEFONES =====

  // Adicionar um novo telefone
  adicionarTelefone(tipo: TipoTelefoneLead, numero: string, descricao?: string): CrmTelefoneLead {
    if (!this.telefones) this.telefones = [];

    // Limpar e validar número
    const numeroLimpo = CrmTelefoneLead.limparNumero(numero);
    if (!numeroLimpo) {
      throw new Error('Número de telefone inválido');
    }

    // Verificar se já existe um telefone do mesmo tipo
    const telefoneExistente = this.telefones.find(tel => tel.tipo === tipo);
    if (telefoneExistente) {
      telefoneExistente.numero = numeroLimpo;
      telefoneExistente.descricao = descricao;
      telefoneExistente.ativo = true;
      return telefoneExistente;
    }

    const novoTelefone = new CrmTelefoneLead(this.id || 0, tipo, numeroLimpo, descricao, this.telefones.length);
    this.telefones.push(novoTelefone);
    this.sincronizarTelefonePrincipal();
    return novoTelefone;
  }

  // Remover telefone por tipo
  removerTelefone(tipo: TipoTelefoneLead): boolean {
    if (!this.telefones) return false;

    const index = this.telefones.findIndex(tel => tel.tipo === tipo);
    if (index >= 0) {
      this.telefones.splice(index, 1);
      this.sincronizarTelefonePrincipal();
      return true;
    }
    return false;
  }

  // Obter telefone por tipo
  getTelefonePorTipo(tipo: TipoTelefoneLead): CrmTelefoneLead | undefined {
    return this.telefones?.find(telefone => telefone.tipo === tipo && telefone.ativo);
  }

  // Obter todos os telefones ativos
  getTelefonesAtivos(): CrmTelefoneLead[] {
    return this.telefones?.filter(telefone => telefone.ativo) || [];
  }

  // Obter telefone principal (WhatsApp > Celular > primeiro ativo)
  getTelefonePrincipal(): CrmTelefoneLead | undefined {
    const telefones = this.getTelefonesAtivos();
    if (telefones.length === 0) return undefined;

    // Prioridade: WhatsApp > Celular > outros
    const whatsapp = telefones.find(t => t.tipo === TipoTelefoneLead.WhatsApp);
    if (whatsapp) return whatsapp;

    const celular = telefones.find(t => t.tipo === TipoTelefoneLead.Celular);
    if (celular) return celular;

    return telefones[0];
  }

  // Obter telefone do WhatsApp
  getTelefoneWhatsApp(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.WhatsApp);
  }

  // Obter telefone fixo
  getTelefoneFixo(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.TelefoneFixo);
  }

  // Obter telefone celular
  getTelefoneCelular(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.Celular);
  }

  // Obter telefone comercial
  getTelefoneComercial(): CrmTelefoneLead | undefined {
    return this.getTelefonePorTipo(TipoTelefoneLead.Comercial);
  }

  // Obter URL do WhatsApp (primeiro telefone disponível)
  getWhatsAppUrlTelefone(): string {
    const whatsapp = this.getTelefoneWhatsApp();
    if (whatsapp) return whatsapp.getWhatsAppUrl();

    const celular = this.getTelefoneCelular();
    if (celular) return celular.getWhatsAppUrl();

    // Fallback para o campo telefone original
    if (this.telefone) {
      const numeroLimpo = this.telefone.replace(/\D/g, '');
      return `https://wa.me/55${numeroLimpo}`;
    }

    return '';
  }

  // Sincronizar campo telefone principal com array de telefones (compatibilidade)
  sincronizarTelefonePrincipal(): void {
    const telefonePrincipal = this.getTelefonePrincipal();
    this.telefone = telefonePrincipal?.numero || '';
  }

  // Inicializar telefones a partir do campo telefone existente
  inicializarTelefonesDoTelefone(): void {
    if (this.telefone && !this.getTelefonePrincipal()) {
      const numeroLimpo = CrmTelefoneLead.limparNumero(this.telefone);
      if (numeroLimpo) {
        const tipo = CrmTelefoneLead.detectarTipo(numeroLimpo);
        this.adicionarTelefone(tipo, numeroLimpo, 'Telefone principal');
      }
    }
  }

  // Validar todos os telefones
  validarTelefones(): { validos: CrmTelefoneLead[], invalidos: CrmTelefoneLead[] } {
    const telefones = this.getTelefonesAtivos();
    return {
      validos: telefones.filter(telefone => telefone.isValid()),
      invalidos: telefones.filter(telefone => !telefone.isValid())
    };
  }

  // Obter todos os números de telefone formatados
  getTelefonesFormatados(): string[] {
    return this.getTelefonesAtivos().map(telefone => telefone.getNumeroFormatado());
  }

  // Obter telefones por categoria
  getTelefonesPorCategoria(): { [categoria: string]: CrmTelefoneLead[] } {
    const telefones = this.getTelefonesAtivos();
    return {
      'Contato Direto': telefones.filter(t => [TipoTelefoneLead.WhatsApp, TipoTelefoneLead.Celular].includes(t.tipo)),
      'Comercial': telefones.filter(t => [TipoTelefoneLead.TelefoneFixo, TipoTelefoneLead.Comercial].includes(t.tipo)),
      'Emergência': telefones.filter(t => t.tipo === TipoTelefoneLead.Emergencia)
    };
  }

  // Contar telefones ativos
  contarTelefones(): number {
    return this.getTelefonesAtivos().length;
  }

  // Adicionar telefones a partir de array de dados
  adicionarTelefonesFromArray(telefonesData: any[]): void {
    if (!telefonesData || !Array.isArray(telefonesData)) return;

    telefonesData.forEach((telefoneData, index) => {
      const tipo = telefoneData.tipo as TipoTelefoneLead;
      const numero = telefoneData.numero;
      const descricao = telefoneData.descricao;

      if (tipo && numero) {
        this.adicionarTelefone(tipo, numero, descricao);
      }
    });
  }

  // ===== MÉTODOS PARA GERENCIAR CONCORRENTE =====

  /**
   * Detecta automaticamente o concorrente baseado nos links do tipo "Concorrente"
   */
  detectarConcorrente(): ConcorrenteLead | null {
    const competitorLinks = this.links?.filter(link => link.tipo === TipoLinkLead.Concorrente && link.ativo);

    if (competitorLinks && competitorLinks.length > 0) {
      // Tenta detectar o concorrente pela URL
      for (const link of competitorLinks) {
        const detected = this.parseCompetitorFromUrl(link.url);
        if (detected) return detected;
      }

      // Se não conseguiu detectar pela URL, tenta pela descrição
      for (const link of competitorLinks) {
        if (link.descricao) {
          const detected = this.parseCompetitorFromDescription(link.descricao);
          if (detected) return detected;
        }
      }
    }

    return null;
  }

  /**
   * Analisa uma URL para identificar o sistema concorrente
   */
  private parseCompetitorFromUrl(url: string): ConcorrenteLead | null {
    if (!url) return null;

    const urlLower = url.toLowerCase();

    // Mapeamento de domínios/padrões para concorrentes
    const competitorPatterns: { [pattern: string]: ConcorrenteLead } = {
      'amo.delivery': ConcorrenteLead.AmoDelivery,
      'appparadelivery.com': ConcorrenteLead.AppParaDelivery,
      'bigdim.com.br': ConcorrenteLead.Bigdim,
      'byappfood.com': ConcorrenteLead.ByAppFood,
      'byfood.com.br': ConcorrenteLead.ByFood,
      'cardapio.co': ConcorrenteLead.CardapioCo,
      'cardapiofacil.digital': ConcorrenteLead.CardapioFacil,
      'cardapiopronto.com.br': ConcorrenteLead.CardapioPronto,
      'cardapioweb.com': ConcorrenteLead.Cardapioweb,
      'cardapi.us': ConcorrenteLead.Cardapius,
      'ccmpedidoonline.com.br': ConcorrenteLead.CCMPedidoOnline,
      'cinndi.com.br': ConcorrenteLead.Cinndi,
      'deliveryseguro.com': ConcorrenteLead.DeliverySeguro,
      'delyver.com.br': ConcorrenteLead.Delyver,
      'ecta.com.br': ConcorrenteLead.Ecta,
      'eita.delivery': ConcorrenteLead.EitaDelivery,
      'expressodelivery.com.br': ConcorrenteLead.ExpressoDelivery,
      'expressomenu.com.br': ConcorrenteLead.ExpressoMenu,
      'go2gosolutions.com.br': ConcorrenteLead.Go2GoSolutions,
      'goentrega.online': ConcorrenteLead.GoEntrega,
      'goomer': ConcorrenteLead.GoomerGratuito, // Será refinado pela análise de plano
      'grandchef.com.br': ConcorrenteLead.GrandChef,
      'instabuy.com.br': ConcorrenteLead.Instabuy,
      'vendaskuppi.com.br': ConcorrenteLead.Kuppi,
      'laddelivery.com': ConcorrenteLead.LadDelivery,
      'magnata.app': ConcorrenteLead.MagnataApp,
      'menuintegrado.com.br': ConcorrenteLead.MenuIntegrado,
      'menuvem.com.br': ConcorrenteLead.Menuvem,
      'meucardapiodigital.com.br': ConcorrenteLead.MeuCardapioDigital,
      'meupedido.delivery': ConcorrenteLead.MeuPedido,
      'menap.com.br': ConcorrenteLead.Menap,
      'millerdelivery.com.br': ConcorrenteLead.MillerDelivery,
      'pediaki.com.br': ConcorrenteLead.Pediaki,
      'pedir.online': ConcorrenteLead.PedirOnline,
      'pedyun.com.br': ConcorrenteLead.Pedyun,
      'pedzap.com.br': ConcorrenteLead.PedZap,
      'podepedir.com.br': ConcorrenteLead.PodePedir,
      'popsales.com.br': ConcorrenteLead.PopSales,
      'starfood.com.br': ConcorrenteLead.StarFood,
      'vtto.com.br': ConcorrenteLead.Vtto,
      'zappedis.com.br': ConcorrenteLead.Zappedis,
      'wabiz.com.br': ConcorrenteLead.Wabiz,
      'webcardapio.com.br': ConcorrenteLead.Webcardapio,
      'alloy.al': ConcorrenteLead.Alloy,
      'stilowebdelivery.com.br': ConcorrenteLead.StiloWebDelivery,
      'tuigoeats.app.br': ConcorrenteLead.TuigoEats
    };

    // Procura por padrões na URL
    for (const [pattern, competitor] of Object.entries(competitorPatterns)) {
      if (urlLower.includes(pattern)) {
        return competitor;
      }
    }

    return null;
  }

  /**
   * Analisa uma descrição para identificar o sistema concorrente
   */
  private parseCompetitorFromDescription(description: string): ConcorrenteLead | null {
    if (!description) return null;

    const descLower = description.toLowerCase();

    // Mapeamento de termos na descrição para concorrentes
    const descriptionPatterns: { [pattern: string]: ConcorrenteLead } = {
      'amo delivery': ConcorrenteLead.AmoDelivery,
      'anota ai': ConcorrenteLead.AnotaAi,
      'app para delivery': ConcorrenteLead.AppParaDelivery,
      'beetech': ConcorrenteLead.Beetech,
      'bigdim': ConcorrenteLead.Bigdim,
      'by app food': ConcorrenteLead.ByAppFood,
      'by food': ConcorrenteLead.ByFood,
      'cardapio.co': ConcorrenteLead.CardapioCo,
      'cardápio fácil': ConcorrenteLead.CardapioFacil,
      'cardápio pronto': ConcorrenteLead.CardapioPronto,
      'cardapioweb': ConcorrenteLead.Cardapioweb,
      'cardapius': ConcorrenteLead.Cardapius,
      'ccm pedido online': ConcorrenteLead.CCMPedidoOnline,
      'cinndi': ConcorrenteLead.Cinndi,
      'delivery direto': ConcorrenteLead.DeliveryDireto,
      'delivery seguro': ConcorrenteLead.DeliverySeguro,
      'delyver': ConcorrenteLead.Delyver,
      'ecta': ConcorrenteLead.Ecta,
      'eita delivery': ConcorrenteLead.EitaDelivery,
      'expresso delivery': ConcorrenteLead.ExpressoDelivery,
      'expresso menu': ConcorrenteLead.ExpressoMenu,
      'glow delivery': ConcorrenteLead.GlowDelivery,
      'go2go solutions': ConcorrenteLead.Go2GoSolutions,
      'goentrega': ConcorrenteLead.GoEntrega,
      'goomer': ConcorrenteLead.GoomerGratuito,
      'grand chef': ConcorrenteLead.GrandChef,
      'instabuy': ConcorrenteLead.Instabuy,
      'instadelivery': ConcorrenteLead.Instadelivery,
      'jotaja': ConcorrenteLead.Jotaja,
      'kyte': ConcorrenteLead.Kyte,
      'kuppi': ConcorrenteLead.Kuppi,
      'lad delivery': ConcorrenteLead.LadDelivery,
      'magnata app': ConcorrenteLead.MagnataApp,
      'menu integrado': ConcorrenteLead.MenuIntegrado,
      'menuvem': ConcorrenteLead.Menuvem,
      'meu cardápio digital': ConcorrenteLead.MeuCardapioDigital,
      'meupedido': ConcorrenteLead.MeuPedido,
      'menap': ConcorrenteLead.Menap,
      'menu dino': ConcorrenteLead.MenuDino,
      'menu ifood': ConcorrenteLead.MenuIfood,
      'miller delivery': ConcorrenteLead.MillerDelivery,
      'neemo': ConcorrenteLead.Neemo,
      'ola click': ConcorrenteLead.OlaClick,
      'pedefacil': ConcorrenteLead.PedefacilUOL,
      'pediaki': ConcorrenteLead.Pediaki,
      'pedir delivery': ConcorrenteLead.PedirDelivery,
      'pedir online': ConcorrenteLead.PedirOnline,
      'pedyun': ConcorrenteLead.Pedyun,
      'pedzap': ConcorrenteLead.PedZap,
      'pode pedir': ConcorrenteLead.PodePedir,
      'popsales': ConcorrenteLead.PopSales,
      'prefiro delivery': ConcorrenteLead.PrefiroDelivery,
      'rvpedidos': ConcorrenteLead.Rvpedidos,
      'saipos cardápio': ConcorrenteLead.SaiposCardapio,
      'stayapp': ConcorrenteLead.StayApp,
      'starfood': ConcorrenteLead.StarFood,
      'vtto': ConcorrenteLead.Vtto,
      'zappedis': ConcorrenteLead.Zappedis,
      'wabiz': ConcorrenteLead.Wabiz,
      'webcardapio': ConcorrenteLead.Webcardapio,
      'whats menu': ConcorrenteLead.WhatsMenu,
      'alloy': ConcorrenteLead.Alloy,
      'cardápio digital totvs': ConcorrenteLead.CardapioDigitalTotvs,
      'pedir agora': ConcorrenteLead.PedirAgora,
      'stilo web delivery': ConcorrenteLead.StiloWebDelivery,
      'tuigo eats': ConcorrenteLead.TuigoEats,
      'hubt': ConcorrenteLead.Hubt
    };

    // Procura por termos na descrição
    for (const [pattern, competitor] of Object.entries(descriptionPatterns)) {
      if (descLower.includes(pattern)) {
        return competitor;
      }
    }

    return null;
  }

  /**
   * Obtém o concorrente do lead (detectado automaticamente ou definido manualmente)
   */
  getConcorrente(): ConcorrenteLead {
    // Se já foi definido manualmente, retorna o valor
    if (this.concorrente) return this.concorrente;

    // Tenta detectar automaticamente
    const detected = this.detectarConcorrente();
    return detected || ConcorrenteLead.NaoDescoberto;
  }

  /**
   * Define manualmente o concorrente do lead
   */
  setConcorrente(concorrente: ConcorrenteLead): void {
    this.concorrente = concorrente;
  }

  /**
   * Retorna se o lead tem um concorrente conhecido
   */
  hasConcorrente(): boolean {
    const concorrente = this.getConcorrente();
    return concorrente !== ConcorrenteLead.NaoDescoberto && concorrente !== ConcorrenteLead.NaoTemSistema;
  }

  /**
   * Retorna a cor do badge do concorrente para exibição
   */
  getCorConcorrente(): string {
    const concorrente = this.getConcorrente();

    switch (concorrente) {
      case ConcorrenteLead.NaoDescoberto:
        return '#6c757d'; // Cinza
      case ConcorrenteLead.NaoTemSistema:
        return '#28a745'; // Verde (oportunidade)
      default:
        return '#dc3545'; // Vermelho (concorrente ativo)
    }
  }

  /**
   * Retorna o ícone do concorrente para exibição
   */
  getIconeConcorrente(): string {
    const concorrente = this.getConcorrente();

    switch (concorrente) {
      case ConcorrenteLead.NaoDescoberto:
        return 'fa-question-circle';
      case ConcorrenteLead.NaoTemSistema:
        return 'fa-plus-circle';
      default:
        return 'fa-exclamation-triangle';
    }
  }

  // ===== MÉTODOS PARA GERENCIAR SÓCIOS =====

  /**
   * Obtém todos os sócios da empresa associada ao lead
   */
  getSocios(): Socio[] {
    return this.crmEmpresa?.getSocios() || [];
  }

  /**
   * Obtém o sócio principal da empresa
   */
  getSocioPrincipal(): Socio | undefined {
    return this.crmEmpresa?.getSocioPrincipal();
  }

  /**
   * Verifica se o lead tem sócios cadastrados
   */
  hasSocios(): boolean {
    return this.crmEmpresa?.hasSocios() || false;
  }

  /**
   * Obtém a quantidade de sócios
   */
  getQuantidadeSocios(): number {
    return this.crmEmpresa?.getQuantidadeSocios() || 0;
  }

  /**
   * Obtém sócios secundários (todos exceto o principal)
   */
  getSociosSecundarios(): Socio[] {
    const socios = this.getSocios();
    return socios.filter(socio => !socio.principal);
  }

  /**
   * Obtém o nome do responsável principal
   * Prioriza o sócio principal, senão usa o nomeResponsavel do lead
   */
  getNomeResponsavelPrincipal(): string {
    const socioPrincipal = this.getSocioPrincipal();
    return socioPrincipal?.nome || this.nomeResponsavel;
  }

  /**
   * Obtém todos os nomes de responsáveis (sócio principal + responsável do lead se diferente)
   */
  getTodosResponsaveis(): string[] {
    const responsaveis: string[] = [];

    const socioPrincipal = this.getSocioPrincipal();
    if (socioPrincipal?.nome) {
      responsaveis.push(socioPrincipal.nome);
    }

    // Adicionar nomeResponsavel se for diferente do sócio principal
    if (this.nomeResponsavel &&
        this.nomeResponsavel !== socioPrincipal?.nome) {
      responsaveis.push(this.nomeResponsavel);
    }

    // Adicionar outros sócios
    const sociosSecundarios = this.getSociosSecundarios();
    sociosSecundarios.forEach(socio => {
      if (socio.nome && !responsaveis.includes(socio.nome)) {
        responsaveis.push(socio.nome);
      }
    });

    return responsaveis;
  }

  /**
   * Verifica se um nome está entre os responsáveis/sócios
   */
  isResponsavel(nome: string): boolean {
    const todosResponsaveis = this.getTodosResponsaveis();
    return todosResponsaveis.some(resp =>
      resp.toLowerCase().includes(nome.toLowerCase()) ||
      nome.toLowerCase().includes(resp.toLowerCase())
    );
  }
}
