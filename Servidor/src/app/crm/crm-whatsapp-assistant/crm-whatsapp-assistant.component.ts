import { Component, OnInit, HostListener } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LeadService } from '../services/lead.service';
import { SugestoesService } from '../services/sugestoes.service';

@Component({
  selector: 'app-crm-whatsapp-assistant',
  templateUrl: './crm-whatsapp-assistant.component.html',
  styleUrls: ['./crm-whatsapp-assistant.component.scss']
})
export class CrmWhatsappAssistantComponent implements OnInit {
  // Dados do contato
  telefone: string = '';
  contato: any = {
    nome: '',
    telefone: '',
    empresa: '',
    email: '',
    instagramHandle: '',
    sistemaConcorrente: '',
    bioInsta: '',
    id: null
  };
  
  // Flag para indicar se está rodando em iframe
  private isInIframe: boolean = false;

  // Estado da conversa
  faseSpin: 'rapport' | 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto' = 'situacao';
  contextoResumo: string = '';
  faseDetectadaAutomaticamente: string | null = null;

  // Mensagens capturadas do WhatsApp
  mensagensCapturadas: any[] = [];
  capturandoMensagens: boolean = false;

  // Sugestões de resposta (agora suporta múltiplas)
  sugestoes: any[] = [];
  sugestaoSelecionada: number = 0;
  carregando: boolean = false;
  erro: string | null = null;

  // Configurações
  tipoTom: 'formal' | 'informal' | 'tecnico' = 'formal';
  produto: string = 'Meu Cardápio';

  // Modo rapport/atratividade
  modoRapport: boolean = false;
  tipoAbordagem: 'direta' | 'indireta' | 'consultiva' = 'consultiva';
  
  // Modo apresentação
  modoApresentacao: boolean = false;
  
  // Estado de carregamento inicial
  carregandoDados: boolean = true;
  
  // Dados para novo lead
  novoLead: any = {
    empresa: '',
    email: '',
    observacoes: ''
  };
  
  // Nome do contato vindo do WhatsApp
  nomeWhatsApp: string = '';

  // Fase detectada pela IA
  faseDetectadaPelaIA: string | null = null;
  confiancaDeteccao: number = 0;
  dataUltimaDeteccao: Date | null = null;
  detectandoFase: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private leadService: LeadService,
    private sugestoesService: SugestoesService
  ) { }

  ngOnInit(): void {
    // Detectar se está em iframe
    this.isInIframe = this.inIframe();
    console.log('[WhatsApp Assistant] Rodando em iframe:', this.isInIframe);
    
    // Começar com loading ativo
    this.carregandoDados = true;
    
    this.route.params.subscribe(params => {
      // Garantir que loading sempre começa como true
      this.carregandoDados = true;
      
      // Resetar TODOS os estados para valores neutros
      this.contato = { 
        nome: '', 
        telefone: '', 
        empresa: '', 
        email: '', 
        instagramHandle: '',
        sistemaConcorrente: '',
        bioInsta: '', 
        id: null 
      };
      this.sugestoes = [];
      this.mensagensCapturadas = [];
      this.contextoResumo = 'Aguardando captura de mensagens...';
      this.erro = null;
      this.carregando = false;
      this.capturandoMensagens = false;
      
      // Resetar dados do novo lead
      this.novoLead = {
        empresa: '',
        email: '',
        observacoes: ''
      };
      
      // Resetar nome do WhatsApp
      this.nomeWhatsApp = '';
      
      // Carregar novo contato
      this.telefone = params['telefone'];
      this.carregarDadosContato();
      this.carregarContextoConversa();
      
      // Indicar que está pronto para receber mensagens
      this.capturandoMensagens = true;
    });
  }

  carregarDadosContato(): void {
    // Garantir que loading está ativo
    this.carregandoDados = true;
    
    if (this.telefone) {
      console.log('[WhatsApp Assistant] Buscando lead por telefone:', this.telefone);
      
      // Primeiro definir o telefone que já temos
      this.contato.telefone = this.telefone;
      
      this.leadService.buscarPorTelefone(this.telefone).then(
        (response) => {
          console.log('[WhatsApp Assistant] Resposta da busca no Bitrix24:', response);

          if (response && response.leadPrincipal) {
            // Usar leadConvertido se disponível, senão usar leadPrincipal
            const lead = response.leadConvertido || response.leadPrincipal;
            console.log('[WhatsApp Assistant] Usando lead:', response.leadConvertido ? 'convertido' : 'principal');

            // Extrair dados do lead do Bitrix24
            this.contato = {
              nome: lead.nomeResponsavel || lead.nomeCompleto || lead.nome || 'Sem nome',
              telefone: this.telefone,
              empresa: lead.empresa || lead.crmEmpresa?.nome || '',
              email: this.extrairEmailPrincipal(lead.emails || []),
              instagramHandle: lead.instagramHandle || this.extrairInstagramHandle(lead.links || []),
              sistemaConcorrente: '', // Não disponível no Bitrix24
              bioInsta: '', // Não disponível no Bitrix24
              id: lead.id,
              score: lead.score || 0,
              origem: lead.origem,
              etapa: lead.etapa,
              segmento: lead.segmento,
              valorPotencial: lead.valorPotencial || 0,
              dataCriacao: lead.dataCriacao,
              observacoes: lead.observacoes || '',
              // Dados específicos do Bitrix24
              dadosBitrix: {
                leadPrincipal: response.leadPrincipal,
                outrosLeads: response.outrosLeads || [],
                telefoneConsultado: response.telefoneConsultado,
                dataConsulta: response.dataConsulta
              }
            };

            // Extrair links úteis
            if (lead.links && Array.isArray(lead.links)) {
              this.contato.website = this.extrairWebsitePrincipal(lead.links);
              this.contato.whatsappLink = this.extrairWhatsappLink(lead.links);
              this.contato.cardapioLink = this.extrairCardapioLink(lead.links);
            }

            console.log('[WhatsApp Assistant] Lead do Bitrix24 processado:', this.contato);
            console.log('[WhatsApp Assistant] Outros leads encontrados:', response.outrosLeads?.length || 0);

          } else if (response && response.leadPrincipal === null) {
            // Nenhum lead encontrado no Bitrix24
            this.contato = {
              nome: 'Contato não encontrado no Bitrix24',
              telefone: this.telefone,
              empresa: '',
              email: '',
              instagramHandle: '',
              sistemaConcorrente: '',
              bioInsta: '',
              id: null,
              dadosBitrix: {
                telefoneConsultado: response.telefoneConsultado,
                mensagem: response.mensagem || 'Nenhum lead encontrado'
              }
            };
            console.log('[WhatsApp Assistant] Nenhum lead encontrado no Bitrix24');
          } else {
            // Resposta inesperada
            this.contato = {
              nome: 'Erro na consulta',
              telefone: this.telefone,
              empresa: '',
              email: '',
              instagramHandle: '',
              sistemaConcorrente: '',
              bioInsta: '',
              id: null
            };
            console.log('[WhatsApp Assistant] Resposta inesperada da API');
          }
          
          setTimeout(() => {
            this.carregandoDados = false;
          }, 200);
        }
      ).catch(erro => {
        console.error('[WhatsApp Assistant] Erro ao buscar lead:', erro);
        this.contato = {
          nome: 'Contato não cadastrado',
          telefone: this.telefone,
          empresa: '',
          email: '',
          instagramHandle: '',
          sistemaConcorrente: '',
          bioInsta: '',
          id: null
        };
        
        setTimeout(() => {
          this.carregandoDados = false;
        }, 200);
      });
    } else {
      this.contato = {
        nome: 'Telefone não informado',
        telefone: '',
        empresa: '',
        email: '',
        instagramHandle: '',
        sistemaConcorrente: '',
        bioInsta: '',
        id: null
      };
      
      setTimeout(() => {
        this.carregandoDados = false;
      }, 200);
    }
  }

  carregarContextoConversa(): void {
    // O contexto será atualizado quando receber mensagens do WhatsApp via content script
    this.contextoResumo = 'Aguardando captura de mensagens...';
  }

  async gerarSugestao(): Promise<void> {
    this.carregando = true;
    this.erro = null;

    try {
      // Se está em modo apresentação, gerar mensagem de apresentação
      if (this.modoApresentacao) {
        await this.gerarMensagemApresentacao();
        return;
      }
      
      // Se está em modo rapport, gerar mensagem de rapport
      if (this.modoRapport) {
        await this.gerarMensagemRapport();
        return;
      }

      // Solicitar mensagens atualizadas do WhatsApp
      await this.solicitarMensagensAtuais();

      // Filtrar mensagens sem texto antes de processar
      const mensagensFiltradas = this.mensagensCapturadas.filter(msg => {
        const textoLimpo = (msg.texto || '').trim();
        if (!textoLimpo || textoLimpo === '[Mensagem sem texto]') {
          console.log('[WhatsApp Assistant] Ignorando mensagem sem texto:', msg);
          return false;
        }
        return true;
      });
      
      // Usar as mensagens filtradas (pode ser array vazio)
      this.mensagensCapturadas = mensagensFiltradas;
      
      if (mensagensFiltradas.length === 0) {
        console.log('[WhatsApp Assistant] Nenhuma mensagem capturada, gerando sugestão inicial');
      }

      // Preparar contexto para gerar sugestão
      const contexto = {
        telefone: this.telefone,
        mensagens: this.mensagensCapturadas,
        etapaFunil: this.mapearFaseSpinParaEtapa(this.faseSpin),
        tomConversa: this.tipoTom === 'formal' ? 'Consultivo' :
                     this.tipoTom === 'informal' ? 'Empático' : 'Técnico',
        produto: this.produto
      };

      // Chamar serviço de sugestões com IA
      console.log('[WhatsApp Assistant] Gerando sugestão com contexto:', contexto);
      const inicio = Date.now();
      this.sugestoesService.gerarSugestoesComIA(contexto).subscribe(
        (response: any) => {
          const tempoResposta = Date.now() - inicio;
          console.log('[WhatsApp Assistant] Resposta completa recebida:', response);

          // Verificar se a resposta tem o formato esperado
          let sugestoes = response;
          let faseSugerida = null;
          let observacoes = null;
          
          // Se response for um objeto com sugestoes dentro
          if (response && !Array.isArray(response) && response.sugestoes) {
            sugestoes = response.sugestoes;
            faseSugerida = response.faseSugerida;
            observacoes = response.observacoes;
          }

          if (sugestoes && sugestoes.length > 0) {
            console.log('[WhatsApp Assistant] Sugestões recebidas do serviço:', sugestoes);
            console.log('[WhatsApp Assistant] Quantidade de sugestões:', sugestoes.length);
            console.log('[WhatsApp Assistant] Fase sugerida:', faseSugerida);
            console.log('[WhatsApp Assistant] Observações:', observacoes);
            
            // Armazenar todas as sugestões
            this.sugestoes = sugestoes.map((sug: any) => ({
              texto: sug.texto,
              confianca: sug.confianca || 0.85,
              faseSpin: sug.faseSpin || faseSugerida || this.faseSpin,
              timestamp: new Date()
            }));
            
            // Se houver fase sugerida e observações, atualizar o componente
            if (faseSugerida) {
              this.faseSpin = faseSugerida as 'rapport' | 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto';
              if (observacoes) {
                this.faseDetectadaAutomaticamente = observacoes;
              }
            }
            
            console.log('[WhatsApp Assistant] Sugestões após mapeamento:', this.sugestoes);
            console.log('[WhatsApp Assistant] this.sugestoes.length:', this.sugestoes.length);
            
            // Resetar seleção
            this.sugestaoSelecionada = 0;

            // Salvar analytics do uso
            this.salvarUsoSugestao(tempoResposta, false);
          } else {
            // Mostrar erro se não conseguiu gerar sugestão
            this.erro = 'Não foi possível gerar uma sugestão. Por favor, tente novamente.';
            this.sugestoes = [];
          }
          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao gerar sugestão:', erro);
          // Mostrar erro ao invés de usar fallback
          this.erro = 'Erro ao conectar com o servidor de IA. Por favor, tente novamente.';
          this.sugestoes = [];
          this.carregando = false;
        }
      );
    } catch (error) {
      this.erro = 'Erro ao gerar sugestão. Tente novamente.';
      console.error('Erro ao gerar sugestão:', error);
      this.carregando = false;
    }
  }

  private async simularChamadaIA(): Promise<void> {
    // Simula delay da API
    return new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Método removido - agora sempre usamos IA para gerar sugestões
  // private gerarSugestaoMockada(): string {
  //   const sugestoesPorFase = {
  //     situacao: `Oi ${this.contato.nome}! Obrigado por entrar em contato. Para entender melhor como posso ajudar sua empresa, você poderia me contar como vocês fazem o controle de estoque atualmente nas 3 lojas?`,
  //     problema: `Entendo sua preocupação, ${this.contato.nome}. Essas dificuldades com estoque são bem comuns. Vocês já enfrentaram situações de ruptura de produtos ou excesso de estoque parado?`,
  //     implicacao: `Imagino que isso deve impactar bastante o resultado das lojas, ${this.contato.nome}. Quanto tempo a equipe perde por dia fazendo esse controle manual? E como isso afeta as vendas quando um produto está em falta?`,
  //     necessidade: `Perfeito, ${this.contato.nome}! Se conseguíssemos automatizar esse processo e dar visibilidade em tempo real do estoque das 3 lojas, qual seria o impacto para vocês? Gostaria de ver como o PromoKit pode resolver exatamente essas questões?`
  //   };

  //   return sugestoesPorFase[this.faseSpin];
  // }

  mudarFaseSpin(novaFase: 'rapport' | 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto'): void {
    this.faseSpin = novaFase;
    this.sugestoes = []; // Limpa sugestões anteriores
    this.sugestaoSelecionada = 0;
    // Limpar fase detectada se mudar manualmente
    if (novaFase !== 'auto') {
      this.faseDetectadaAutomaticamente = null;
    }
  }

  usarSugestao(): void {
    if (this.sugestoes.length > 0 && this.sugestoes[this.sugestaoSelecionada]) {
      const sugestaoAtual = this.sugestoes[this.sugestaoSelecionada];
      // Tentar copiar usando a API moderna de clipboard
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(sugestaoAtual.texto)
          .then(() => {
            console.log('Sugestão copiada com sucesso!');
            this.mostrarFeedback('Sugestão copiada para área de transferência!', 'success');
            // Salvar que a sugestão foi usada
            this.salvarUsoSugestao(0, true);
          })
          .catch((err) => {
            console.error('Erro ao copiar com clipboard API:', err);
            // Tentar método alternativo
            this.copiarTextoFallback(sugestaoAtual.texto);
          });
      } else {
        // Usar método alternativo se clipboard API não estiver disponível
        this.copiarTextoFallback(sugestaoAtual.texto);
      }
    }
  }

  // Copiar sugestão específica
  copiarSugestao(index: number): void {
    if (this.sugestoes[index]) {
      const sugestao = this.sugestoes[index];
      // Tentar copiar usando a API moderna de clipboard
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(sugestao.texto)
          .then(() => {
            console.log('Sugestão copiada com sucesso!');
            this.mostrarFeedback('Sugestão copiada para área de transferência!', 'success');
            // Salvar que a sugestão foi usada
            this.sugestaoSelecionada = index;
            this.salvarUsoSugestao(0, true);
          })
          .catch((err) => {
            console.error('Erro ao copiar com clipboard API:', err);
            // Tentar método alternativo
            this.copiarTextoFallback(sugestao.texto);
          });
      } else {
        // Usar método alternativo se clipboard API não estiver disponível
        this.copiarTextoFallback(sugestao.texto);
      }
    }
  }

  // Editar sugestão individual
  editarSugestaoIndividual(index: number): void {
    // Por enquanto, apenas seleciona a sugestão
    // Pode ser expandido para abrir um modal de edição
    this.sugestaoSelecionada = index;
    console.log('Editando sugestão', index);
  }

  // Método alternativo para copiar texto
  private copiarTextoFallback(texto: string): void {
    try {
      // Criar elemento textarea temporário
      const textarea = document.createElement('textarea');
      textarea.value = texto;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';

      document.body.appendChild(textarea);
      textarea.select();
      textarea.setSelectionRange(0, 99999); // Para mobile

      const sucesso = document.execCommand('copy');
      document.body.removeChild(textarea);

      if (sucesso) {
        console.log('Sugestão copiada com método fallback!');
        this.mostrarFeedback('Sugestão copiada para área de transferência!', 'success');
        this.salvarUsoSugestao(0, true);
      } else {
        console.error('Falha ao copiar texto');
        this.mostrarFeedback('Erro ao copiar. Selecione e copie manualmente.', 'error');
      }
    } catch (err) {
      console.error('Erro no método fallback:', err);
      this.mostrarFeedback('Erro ao copiar. Selecione e copie manualmente.', 'error');
    }
  }



  editarSugestao(): void {
    // TODO: Implementar modal de edição ou tornar textarea editável
    console.log('Modo edição ativado');
  }

  async regenerarSugestao(): Promise<void> {
    await this.gerarSugestao();
  }

  // Selecionar uma sugestão específica
  selecionarSugestao(index: number): void {
    if (index >= 0 && index < this.sugestoes.length) {
      this.sugestaoSelecionada = index;
    }
  }

  getFaseBadgeClass(fase: string): string {
    if (this.faseSpin === fase) {
      return fase === 'auto' ? 'btn btn-info' : 'btn btn-primary';
    }
    return 'btn btn-outline-secondary';
  }

  // Listener para receber mensagens do content script
  @HostListener('window:message', ['$event'])
  handleMessage(event: MessageEvent): void {
    console.log('[WhatsApp Assistant] Mensagem recebida no HostListener:', event.data);
    if (event.data) {
      // Receber informações do contato selecionado
      if (event.data.tipo === 'SELECIONOU_CONTATO' && event.data.payload) {
        const payload = event.data.payload;
        console.log('[WhatsApp Assistant] Recebido SELECIONOU_CONTATO:', payload);
        
        // Sempre armazenar o nome do WhatsApp
        if (payload.nome) {
          this.nomeWhatsApp = payload.nome;
          console.log('[WhatsApp Assistant] Nome do WhatsApp:', this.nomeWhatsApp);
        }
      }

      // Receber contexto da conversa com mensagens
      if (event.data.tipo === 'CONTEXTO_CONVERSA') {
        console.log('[WhatsApp Assistant] Recebido CONTEXTO_CONVERSA:', event.data);
        this.capturandoMensagens = false;

        // Capturar nome do contexto também
        if (event.data.nome) {
          this.nomeWhatsApp = event.data.nome;
          console.log('[WhatsApp Assistant] Nome do WhatsApp (contexto):', this.nomeWhatsApp);
        }

        if (event.data.mensagens && Array.isArray(event.data.mensagens)) {
          console.log('[WhatsApp Assistant] Mensagens recebidas para processar:', event.data.mensagens);
          
          // Processar mensagens para corrigir remetente e filtrar sem texto
          this.mensagensCapturadas = event.data.mensagens
            .filter((msg: any) => {
              // Filtrar mensagens sem texto logo na entrada
              const textoLimpo = (msg.texto || '').trim();
              if (!textoLimpo || textoLimpo === '[Mensagem sem texto]') {
                console.log('[WhatsApp Assistant] Ignorando mensagem sem texto na entrada:', msg);
                return false;
              }
              return true;
            })
            .map((msg: any, index: number) => {
            // Debug detalhado de cada mensagem
            console.log(`[WhatsApp Assistant] Processando mensagem ${index}:`, {
              remetenteOriginal: msg.remetente,
              fromMe: msg.fromMe,
              isFromMe: msg.isFromMe,
              isSentByMe: msg.isSentByMe,
              tipo: msg.tipo,
              texto: msg.texto?.substring(0, 50)
            });
            
            // Determinar remetente com múltiplas verificações
            let remetenteCorrigido = 'Lead'; // Padrão é lead
            
            // Verificar múltiplos indicadores de mensagem enviada
            if (msg.fromMe === true || 
                msg.isFromMe === true || 
                msg.isSentByMe === true ||
                msg.tipo === 'saida' || 
                msg.isOutgoing === true ||
                (msg.remetente === 'Eu')) {
              remetenteCorrigido = 'Eu';
            } else if (msg.remetente && msg.remetente !== 'Lead' && 
                       this.nomeWhatsApp && msg.remetente === this.nomeWhatsApp) {
              // Se o remetente é o mesmo nome do contato do WhatsApp, é do Lead
              remetenteCorrigido = 'Lead';
            }
            
            console.log(`[WhatsApp Assistant] Remetente corrigido: ${remetenteCorrigido}`);
            
            return {
              ...msg,
              remetente: remetenteCorrigido,
              // Manter o nome original para exibição se necessário
              nomeOriginal: msg.remetente
            };
          });
          
          console.log('[WhatsApp Assistant] Mensagens processadas:', this.mensagensCapturadas);
          
          // Criar resumo automático das mensagens
          this.criarResumoConversa();
        }
      }
    }
  }

  // Criar resumo da conversa baseado nas mensagens
  private criarResumoConversa(): void {
    if (this.mensagensCapturadas.length > 0) {
      const ultimasMensagens = this.mensagensCapturadas
        .slice(-3)
        .map(m => `${m.remetente}: ${m.texto}`)
        .join(' | ');

      this.contextoResumo = `Últimas mensagens: ${ultimasMensagens}`;
    }
  }

  // Solicitar mensagens atuais do WhatsApp
  private async solicitarMensagensAtuais(): Promise<void> {
    return new Promise((resolve) => {
      const requestId = Date.now();
      console.log('[WhatsApp Assistant] Solicitando mensagens atuais, requestId:', requestId);

      // Listener temporário para a resposta
      const handleResponse = (event: MessageEvent) => {
        console.log('[WhatsApp Assistant] Evento recebido em handleResponse:', event.data);
        if (event.data && event.data.tipo === 'MENSAGENS_ATUALIZADAS' && event.data.requestId === requestId) {
          console.log('[WhatsApp Assistant] Mensagens atualizadas recebidas:', event.data);
          
          if (event.data.mensagens && Array.isArray(event.data.mensagens)) {
            // Filtrar mensagens sem texto também aqui
            this.mensagensCapturadas = event.data.mensagens.filter((msg: any) => {
              const textoLimpo = (msg.texto || '').trim();
              return textoLimpo && textoLimpo !== '[Mensagem sem texto]';
            });
            this.criarResumoConversa();
          }
          
          // Remover o listener após receber a resposta
          window.removeEventListener('message', handleResponse);
          resolve();
        }
      };

      // Adicionar listener
      window.addEventListener('message', handleResponse);

      // Enviar solicitação
      console.log('[WhatsApp Assistant] Enviando solicitação de mensagens, requestId:', requestId);
      const message = {
        tipo: 'SOLICITAR_MENSAGENS_ATUAIS',
        requestId: requestId
      };
      
      // Usar window.parent.postMessage se estiver em iframe
      if (this.isInIframe) {
        console.log('[WhatsApp Assistant] Enviando para parent window (iframe mode)');
        window.parent.postMessage(message, '*');
      } else {
        console.log('[WhatsApp Assistant] Enviando para window (non-iframe mode)');
        window.postMessage(message, '*');
      }
      console.log('[WhatsApp Assistant] Solicitação enviada via postMessage');

      // Timeout de segurança (3 segundos)
      setTimeout(() => {
        window.removeEventListener('message', handleResponse);
        resolve();
      }, 3000);
    });
  }

  // Método para simular recebimento de mensagens (debug)
  simularMensagens(): void {
    const mensagensSimuladas = [
      {
        texto: 'Olá, gostaria de saber mais sobre o sistema',
        remetente: 'Lead',
        horario: '10:30:15',
        tipo: 'text'
      },
      {
        texto: 'Oi! Claro, posso te ajudar. O que você gostaria de saber?',
        remetente: 'Eu',
        horario: '10:31:20',
        tipo: 'text'
      },
      {
        texto: 'Estamos tendo problemas com controle de estoque',
        remetente: 'Lead',
        horario: '10:32:45',
        tipo: 'text'
      },
      {
        texto: 'Quantas lojas vocês possuem?',
        remetente: 'Eu',
        horario: '10:33:10',
        tipo: 'text'
      },
      {
        texto: 'Temos 3 lojas físicas',
        remetente: 'Lead',
        horario: '10:33:55',
        tipo: 'text'
      }
    ];

    this.mensagensCapturadas = mensagensSimuladas;
    this.capturandoMensagens = false;
    this.criarResumoConversa();
  }

  // Método para limpar todas as mensagens
  limparMensagens(): void {
    this.mensagensCapturadas = [];
    this.contextoResumo = '';
    this.capturandoMensagens = true;
  }

  // Detectar e salvar fase SPIN automaticamente
  detectarESalvarFase(): void {
    if (!this.telefone || this.mensagensCapturadas.length === 0) {
      alert('É necessário ter mensagens capturadas para detectar a fase.');
      return;
    }

    this.detectandoFase = true;

    this.sugestoesService.detectarESalvarFaseSpin(this.telefone, this.mensagensCapturadas)
      .subscribe({
        next: (resultado) => {
          this.faseDetectadaPelaIA = resultado.faseSpin;
          this.confiancaDeteccao = resultado.confianca;
          this.dataUltimaDeteccao = new Date();

          // Atualizar o lead local se encontrado
          if (resultado.leadEncontrado && this.contato.id) {
            this.contato.faseSpinDetectada = resultado.faseSpin;
            this.contato.confiancaFaseDetectada = resultado.confianca;
            this.contato.dataUltimaDeteccaoFase = new Date();
          }

          // Mostrar feedback para o usuário
          this.mostrarFeedback(
            `Fase detectada: ${this.getFaseDisplayName(resultado.faseSpin)} (${Math.round(resultado.confianca * 100)}% confiança)`,
            'success'
          );

          console.log('[WhatsApp Assistant] Fase detectada e salva:', resultado);
        },
        error: (erro) => {
          console.error('[WhatsApp Assistant] Erro ao detectar fase:', erro);
          this.mostrarFeedback('Erro ao detectar fase da conversa', 'error');
        },
        complete: () => {
          this.detectandoFase = false;
        }
      });
  }

  // Obter nome amigável da fase
  getFaseDisplayName(fase: string): string {
    const nomes: Record<string, string> = {
      'rapport': 'Rapport',
      'situacao': 'Situação',
      'problema': 'Problema',
      'implicacao': 'Implicação',
      'necessidade': 'Necessidade',
      'auto': 'Auto-detectar'
    };
    return nomes[fase] || fase;
  }

  // Mostrar feedback para o usuário
  private mostrarFeedback(mensagem: string, tipo: 'success' | 'error' | 'info' = 'info'): void {
    // Por enquanto usar console.log, depois pode implementar toast/alert
    console.log(`[${tipo.toUpperCase()}] ${mensagem}`);

    // Implementação simples com alert por enquanto
    if (tipo === 'error') {
      alert(`Erro: ${mensagem}`);
    } else if (tipo === 'success') {
      alert(`Sucesso: ${mensagem}`);
    }
  }

  // Mapear fase SPIN para etapa do funil
  private mapearFaseSpinParaEtapa(fase: string): string {
    const mapeamento: Record<string, string> = {
      'rapport': 'Rapport',
      'situacao': 'Prospecção',
      'problema': 'Qualificação',
      'implicacao': 'Objeção',
      'necessidade': 'Fechamento',
      'auto': 'auto' // Manter 'auto' para que o backend detecte
    };
    return mapeamento[fase] || 'Prospecção';
  }

  // Salvar uso da sugestão para analytics
  private salvarUsoSugestao(tempoResposta: number, usada: boolean): void {
    if (!this.sugestoes.length || !this.sugestoes[this.sugestaoSelecionada]) return;
    
    const sugestaoAtual = this.sugestoes[this.sugestaoSelecionada];

    this.sugestoesService.salvarUsoSugestao({
      telefone: this.telefone,
      faseSpin: this.faseSpin === 'auto' ? 'situacao' : this.faseSpin, // Usar 'situacao' como fallback para analytics
      sugestaoGerada: sugestaoAtual.texto,
      sugestaoUsada: usada,
      tempoResposta: tempoResposta
    }).subscribe(
      () => console.log('Uso de sugestão salvo'),
      (erro) => console.error('Erro ao salvar uso de sugestão:', erro)
    );
  }

  // Gerar mensagem de rapport/atratividade
  async gerarMensagemRapport(): Promise<void> {
    try {
      // Contexto específico para rapport
      const contextoRapport = {
        telefone: this.telefone,
        nomeContato: this.contato.nome,
        empresa: this.contato.empresa,
        tipoAbordagem: this.tipoAbordagem,
        produto: this.produto,
        modoRapport: true,
        ultimaMensagem: this.mensagensCapturadas.length > 0
          ? this.mensagensCapturadas[this.mensagensCapturadas.length - 1].texto
          : null
      };

      console.log('[WhatsApp Assistant] Gerando mensagem de rapport:', contextoRapport);

      // Chamar serviço para gerar mensagens de rapport
      this.sugestoesService.gerarMensagensRapport(contextoRapport).subscribe(
        (sugestoes) => {
          console.log('[WhatsApp Assistant] Resposta de rapport recebida:', sugestoes);
          
          if (sugestoes && sugestoes.length > 0) {
            // Armazenar todas as sugestões
            this.sugestoes = sugestoes.map((sug: any) => ({
              texto: sug.texto,
              confianca: sug.confianca || 0.85,
              faseSpin: 'rapport',
              tipoAbordagem: this.tipoAbordagem,
              timestamp: new Date()
            }));
            
            // Resetar seleção
            this.sugestaoSelecionada = 0;
          } else {
            // Mostrar erro se não conseguiu gerar sugestões
            this.erro = 'Não foi possível gerar mensagens de rapport. Por favor, tente novamente.';
            this.sugestoes = [];
          }
          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao gerar mensagens de rapport:', erro);
          // Mostrar erro ao invés de usar fallback
          this.erro = 'Erro ao conectar com o servidor de IA. Por favor, tente novamente.';
          this.sugestoes = [];
          this.carregando = false;
        }
      );
    } catch (error) {
      console.error('Erro ao gerar mensagem de rapport:', error);
      this.erro = 'Erro ao gerar mensagem. Tente novamente.';
      this.carregando = false;
    }
  }

  // Método removido - agora sempre usamos IA para gerar mensagens de rapport
  // private gerarRapportMockado(): string {
  //   const mensagensRapport = {
  //     direta: [
  //       `Oi ${this.contato.nome || 'você'}! 👋 Vi que sua empresa está crescendo - isso é ótimo! Tenho uma solução que pode ajudar a escalar ainda mais rápido. Posso te mostrar em apenas 5 minutos?`,
  //       `${this.contato.nome || 'Olá'}, notei que empresas como a sua estão economizando até 3h por dia com nossa solução. Que tal descobrir como fazer o mesmo? 🚀`,
  //       `Oi ${this.contato.nome || 'você'}! Separei 3 cases de sucesso de empresas similares à sua. Vale a pena dar uma olhada - pode ser um divisor de águas! Posso enviar?`
  //     ],
  //     indireta: [
  //       `Oi ${this.contato.nome || 'você'}! Como estão as coisas por aí? 😊 Vi algumas novidades interessantes no mercado de ${this.contato.empresa ? 'seu segmento' : 'gestão'} que podem te interessar...`,
  //       `${this.contato.nome || 'Olá'}, espero que esteja tendo um ótimo dia! 🌟 Estava pensando em alguns desafios comuns que empresas enfrentam nessa época do ano. Como vocês estão lidando com isso?`,
  //       `Oi! Acabei de ler um artigo sobre tendências em ${this.contato.empresa ? 'seu mercado' : 'gestão empresarial'} e lembrei de você. Algumas insights bem interessantes! Gostaria de compartilhar?`
  //     ],
  //     consultiva: [
  //       `${this.contato.nome || 'Olá'}, percebi que muitas empresas do seu porte estão buscando formas de otimizar processos. Qual tem sido seu maior desafio operacional atualmente?`,
  //       `Oi ${this.contato.nome || 'você'}! Tenho ajudado empresas a resolver questões específicas de gestão. Curioso(a) para saber: qual área da sua operação consome mais tempo hoje?`,
  //       `${this.contato.nome || 'Olá'}, uma pergunta rápida: se pudesse resolver apenas UM problema na sua empresa hoje, qual seria? Talvez eu possa ajudar com algumas ideias...`
  //     ]
  //   };

  //   const mensagens = mensagensRapport[this.tipoAbordagem];
  //   return mensagens[Math.floor(Math.random() * mensagens.length)];
  // }

  // Gerar mensagem de apresentação
  async gerarMensagemApresentacao(): Promise<void> {
    try {
      console.log('[WhatsApp Assistant] Gerando mensagem de apresentação profissional');
      
      const contextoApresentacao = {
        telefone: this.telefone,
        nomeContato: this.contato.nome || this.nomeWhatsApp,
        empresa: this.contato.empresa,
        produto: this.produto,
        lead: this.contato
      };
      
      // Chamar serviço para gerar mensagens de apresentação
      this.sugestoesService.gerarMensagemApresentacao(contextoApresentacao).subscribe(
        (sugestoes) => {
          console.log('[WhatsApp Assistant] Resposta de apresentação recebida:', sugestoes);
          
          if (sugestoes && sugestoes.length > 0) {
            // Armazenar todas as sugestões
            this.sugestoes = sugestoes.map((sug: any) => ({
              texto: sug.texto,
              confianca: sug.confianca || 0.9,
              faseSpin: 'apresentacao',
              timestamp: new Date()
            }));
            
            // Resetar seleção
            this.sugestaoSelecionada = 0;
            this.faseDetectadaAutomaticamente = 'Modo Apresentação - Primeira abordagem profissional';
          } else {
            // Mostrar erro se não conseguiu gerar sugestões
            this.erro = 'Não foi possível gerar mensagem de apresentação. Por favor, tente novamente.';
            this.sugestoes = [];
          }
          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao gerar mensagem de apresentação:', erro);
          this.erro = 'Erro ao conectar com o servidor de IA. Por favor, tente novamente.';
          this.sugestoes = [];
          this.carregando = false;
        }
      );
    } catch (error) {
      console.error('Erro ao gerar mensagem de apresentação:', error);
      this.erro = 'Erro ao gerar mensagem. Tente novamente.';
      this.carregando = false;
    }
  }

  // Toggle modo rapport
  toggleModoRapport(): void {
    this.modoRapport = !this.modoRapport;
    // Se ativou rapport, desativa apresentação
    if (this.modoRapport) {
      this.modoApresentacao = false;
    }
    this.sugestoes = []; // Limpa sugestões anteriores
  }
  
  // Toggle modo apresentação
  toggleModoApresentacao(): void {
    this.modoApresentacao = !this.modoApresentacao;
    // Se ativou apresentação, desativa rapport
    if (this.modoApresentacao) {
      this.modoRapport = false;
    }
    this.sugestoes = []; // Limpa sugestões anteriores
  }

  // Mudar tipo de abordagem
  mudarTipoAbordagem(tipo: 'direta' | 'indireta' | 'consultiva'): void {
    this.tipoAbordagem = tipo;
    if (this.modoRapport && this.sugestoes.length > 0) {
      // Regenerar se já tem sugestões de rapport
      this.gerarMensagemRapport();
    }
  }

  // Cadastrar lead rapidamente
  async cadastrarLeadRapido(): Promise<void> {
    if (!this.telefone) {
      this.mostrarFeedback('Telefone é obrigatório', 'error');
      return;
    }

    const novoLead = {
      nomeResponsavel: this.nomeWhatsApp || 'Contato WhatsApp',
      empresa: this.novoLead.empresa || `Cliente ${this.telefone}`,
      telefone: this.telefone,
      email: this.novoLead.email,
      observacoes: this.novoLead.observacoes,
      origem: 'WhatsApp',
      crmEmpresaId: 1 // TODO: Pegar o ID da empresa corrente
    };

    try {
      console.log('[WhatsApp Assistant] Cadastrando novo lead:', novoLead);
      const response = await this.leadService.salveLead(novoLead);
      
      // Se chegou aqui, o lead foi salvo com sucesso (response é o lead salvo)
      this.mostrarFeedback('Lead cadastrado com sucesso!', 'success');
      
      // Recarregar dados do contato
      this.carregarDadosContato();
      
      // Limpar formulário
      this.novoLead = {
        empresa: '',
        email: '',
        observacoes: ''
      };
    } catch (erro) {
      console.error('Erro ao cadastrar lead:', erro);
      this.mostrarFeedback('Erro ao cadastrar lead', 'error');
    }
  }
  
  // Método para detectar se está rodando em iframe
  private inIframe(): boolean {
    try {
      return window.self !== window.top;
    } catch (e) {
      return true;
    }
  }

  // Obter informações do concorrente
  getConcorrenteInfo(nomeConcorrente: string): any {
    const concorrentes: Record<string, any> = {
      'iFood': {
        pontosFracos: [
          'Taxa de 27% sobre vendas',
          'Não tem sistema de fidelidade próprio',
          'Cliente não consegue contato direto',
          'Demora para receber pagamentos'
        ],
        diferenciais: [
          'Taxa única de R$ 89/mês',
          'Sistema de fidelidade integrado',
          'WhatsApp direto com cliente',
          'Recebe na hora via Pix'
        ],
        sugestaoAbordagem: 'Foque no custo-benefício e no relacionamento direto com cliente'
      },
      'Rappi': {
        pontosFracos: [
          'Taxa alta e variável',
          'Interface complexa para gerenciar',
          'Suporte limitado',
          'Não permite personalização'
        ],
        diferenciais: [
          'Interface simples e intuitiva',
          'Suporte humanizado via WhatsApp',
          'Totalmente personalizável',
          'Sem taxas variáveis'
        ],
        sugestaoAbordagem: 'Destaque a simplicidade e o suporte humanizado'
      },
      'Goomer': {
        pontosFracos: [
          'Focado apenas em cardápio',
          'Não tem delivery integrado',
          'Precisa de outros sistemas',
          'Custo adicional por funcionalidade'
        ],
        diferenciais: [
          'Sistema completo all-in-one',
          'Delivery próprio integrado',
          'Tudo em uma única plataforma',
          'Preço único com tudo incluído'
        ],
        sugestaoAbordagem: 'Mostre como ter tudo integrado economiza tempo e dinheiro'
      },
      'Aiqfome': {
        pontosFracos: [
          'Taxa sobre vendas',
          'Limitado a algumas cidades',
          'Sistema básico',
          'Pouca customização'
        ],
        diferenciais: [
          'Funciona em qualquer cidade',
          'Sistema completo e robusto',
          'Altamente customizável',
          'Sem taxa sobre vendas'
        ],
        sugestaoAbordagem: 'Enfatize a liberdade geográfica e ausência de taxas'
      },
      'default': {
        pontosFracos: [
          'Sistema genérico',
          'Suporte limitado',
          'Custos adicionais',
          'Pouca integração'
        ],
        diferenciais: [
          'Feito para restaurantes brasileiros',
          'Suporte 24/7 humanizado',
          'Tudo incluído no plano',
          'Integração total'
        ],
        sugestaoAbordagem: 'Pergunte sobre as dificuldades atuais e mostre como resolvemos'
      }
    };

    // Retorna info específica ou default
    return concorrentes[nomeConcorrente] || concorrentes['default'];
  }
}
