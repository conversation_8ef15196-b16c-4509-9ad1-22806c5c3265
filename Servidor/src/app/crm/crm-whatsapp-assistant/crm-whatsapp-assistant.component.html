<!-- WhatsApp Assistant - Design Limpo e Funcional -->
<div class="container-fluid">

  <!-- Loading -->
  <div *ngIf="carregandoDados" class="d-flex flex-column align-items-center justify-content-center" style="min-height: 400px;">
    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
      <span class="sr-only">Carregando...</span>
    </div>
    <h5 class="text-muted">Carregando dados do contato...</h5>
  </div>

  <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
  <div *ngIf="!carregandoDados">

    <!-- Header da <PERSON>a -->
    <div class="row mb-3">
      <div class="col-12">
        <div class="page-title-box">
          <div class="page-title-right">
            <ol class="breadcrumb m-0">
              <li class="breadcrumb-item"><a href="javascript: void(0);">CRM</a></li>
              <li class="breadcrumb-item active">Assistente WhatsApp</li>
            </ol>
          </div>
          <h4 class="page-title">
            <i class="fe-message-square"></i> Assistente de Vendas - WhatsApp
          </h4>
        </div>
      </div>
    </div>

    <!-- Card: Informações Completas do Lead -->
    <div class="card mb-3">
      <div class="card-header bg-primary text-white">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h5 class="mb-0">
              <i class="fe-user me-2"></i>
              {{ contato.nome || nomeWhatsApp || 'Novo Contato' }}
            </h5>
            <small class="opacity-75">
              {{ contato.id ? 'Lead Cadastrado' : 'Novo Lead - Não Cadastrado' }}
            </small>
          </div>
          <div class="text-end">
            <span class="badge bg-warning text-dark mb-1" *ngIf="contato.score">
              <i class="fe-star me-1"></i>
              {{ contato.score }}% Score
            </span>
            <span class="badge bg-warning text-dark mb-1" *ngIf="!contato.score">
              <i class="fe-star me-1"></i>
              Novo Lead
            </span>
            <div class="small" *ngIf="contato.origem">
              <i class="fe-globe me-1"></i>
              Origem: {{ contato.origem }}
            </div>
            <div class="small" *ngIf="!contato.origem">
              <i class="fe-circle text-success me-1"></i>
              Online agora
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <!-- Informações Principais -->
          <div class="col-md-8">
            <div class="row mb-3">
              <div class="col-md-6">
                <h6 class="text-primary mb-2">
                  <i class="fe-briefcase me-1"></i>
                  Empresa:
                </h6>
                <p class="mb-0 fw-bold">{{ contato.empresa || 'Empresa não informada' }}</p>
                <small class="text-muted" *ngIf="contato.segmento">
                  <i class="fe-tag me-1"></i>
                  Segmento: {{ contato.segmento }}
                </small>
              </div>
              <div class="col-md-6">
                <h6 class="text-primary mb-2">
                  <i class="fe-phone me-1"></i>
                  Telefone:
                </h6>
                <p class="mb-0">{{ contato.telefone }}</p>
                <small class="text-muted" *ngIf="contato.valorPotencial && contato.valorPotencial > 0">
                  <i class="fe-dollar-sign me-1"></i>
                  Valor Potencial: R$ {{ contato.valorPotencial | number:'1.2-2' }}
                </small>
              </div>
            </div>

            <div class="row mb-3" *ngIf="contato.email || contato.instagramHandle">
              <div class="col-md-6" *ngIf="contato.email">
                <h6 class="text-primary mb-2">
                  <i class="fe-mail me-1"></i>
                  Email:
                </h6>
                <p class="mb-0">{{ contato.email }}</p>
              </div>
              <div class="col-md-6" *ngIf="contato.instagramHandle">
                <h6 class="text-primary mb-2">
                  <i class="fe-instagram me-1"></i>
                  Instagram:
                </h6>
                <p class="mb-0">
                  <a [href]="'https://instagram.com/' + contato.instagramHandle" target="_blank" class="text-decoration-none">
                    {{ '@' + contato.instagramHandle }}
                  </a>
                </p>
              </div>
            </div>

            <!-- Bio Instagram -->
            <div *ngIf="contato.bioInsta" class="mb-3">
              <h6 class="text-primary mb-2">
                <i class="fe-info me-1"></i>
                Bio Instagram:
              </h6>
              <p class="mb-0 text-muted small">{{ contato.bioInsta }}</p>
            </div>
          </div>

          <!-- Status e Métricas -->
          <div class="col-md-4">
            <div class="row g-2">
              <div class="col-6">
                <div class="text-center p-2 bg-primary text-white rounded">
                  <i class="fe-target fs-4"></i>
                  <div class="small mt-1">
                    <strong>Fase Manual</strong><br>
                    {{ getFaseDisplayName(faseSpin) }}
                  </div>
                </div>
              </div>
              <div class="col-6">
                <div class="text-center p-2 bg-success text-white rounded">
                  <i class="fe-clock fs-4"></i>
                  <div class="small mt-1">
                    <strong>Resposta</strong><br>
                    2 min
                  </div>
                </div>
              </div>
              <div class="col-6">
                <div class="text-center p-2 bg-info text-white rounded">
                  <i class="fe-trending-up fs-4"></i>
                  <div class="small mt-1">
                    <strong>Potencial</strong><br>
                    Alto
                  </div>
                </div>
              </div>
              <div class="col-6">
                <div class="text-center p-2 bg-warning text-dark rounded">
                  <i class="fe-zap fs-4"></i>
                  <div class="small mt-1">
                    <strong>Prioridade</strong><br>
                    Hot
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Alerta Concorrente -->
        <div *ngIf="contato.sistemaConcorrente" class="alert alert-warning mt-3 mb-0">
          <div class="d-flex align-items-center justify-content-between">
            <div>
              <h6 class="alert-heading mb-1">
                <i class="fe-alert-triangle me-2"></i>
                <strong>Usa Concorrente: {{ contato.sistemaConcorrente }}</strong>
              </h6>
              <p class="mb-0">Taxa atual: 27% sobre vendas</p>
            </div>
            <div>
              <span class="badge bg-success fs-6">
                <i class="fe-dollar-sign me-1"></i>
                Economia: R$ 1.500/mês
              </span>
            </div>
          </div>
        </div>

        <!-- Informações do Bitrix24 (se encontrado) -->
        <div *ngIf="contato.id" class="mt-3">
          <div class="alert alert-success">
            <div class="d-flex align-items-center justify-content-between">
              <div>
                <h6 class="alert-heading mb-1">
                  <i class="fe-database me-2"></i>
                  <strong>Lead encontrado no Bitrix24</strong>
                </h6>
                <p class="mb-0">
                  <small>
                    <i class="fe-calendar me-1"></i>
                    Etapa: {{ contato.etapa || 'Não informada' }}
                    <span *ngIf="contato.observacoes" class="ms-3">
                      <i class="fe-file-text me-1"></i>
                      {{ contato.observacoes.substring(0, 50) }}{{ contato.observacoes.length > 50 ? '...' : '' }}
                    </span>
                  </small>
                </p>
              </div>
              <div>
                <span class="badge bg-success fs-6">
                  <i class="fe-check-circle me-1"></i>
                  Sincronizado
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Botão Cadastrar (se novo) -->
        <div *ngIf="!contato.id" class="mt-3 text-end">
          <button class="btn btn-success" data-bs-toggle="collapse" data-bs-target="#capturaRapida">
            <i class="fe-user-plus me-1"></i>
            Cadastrar Lead
          </button>
        </div>
      </div>
    </div>

    <!-- Card: Fase Detectada pela IA -->
    <div class="card mb-3" *ngIf="faseDetectadaPelaIA || mensagensCapturadas.length > 0">
      <div class="card-header bg-info text-white">
        <div class="d-flex align-items-center justify-content-between">
          <h5 class="mb-0">
            <i class="fe-brain me-2"></i>
            Fase Detectada pela IA
          </h5>
          <button class="btn btn-light btn-sm"
                  (click)="detectarESalvarFase()"
                  [disabled]="detectandoFase || mensagensCapturadas.length === 0">
            <i class="fe-refresh-cw me-1" [class.rotating]="detectandoFase"></i>
            {{ detectandoFase ? 'Analisando...' : 'Recalcular' }}
          </button>
        </div>
      </div>
      <div class="card-body">
        <div *ngIf="faseDetectadaPelaIA" class="row align-items-center">
          <div class="col-md-8">
            <h6 class="text-primary mb-1">
              <i class="fe-target me-1"></i>
              Fase Atual: <strong>{{ getFaseDisplayName(faseDetectadaPelaIA) }}</strong>
            </h6>
            <p class="mb-1">
              <i class="fe-zap me-1"></i>
              Confiança: <strong>{{ (confiancaDeteccao * 100) | number:'1.0-0' }}%</strong>
            </p>
            <p class="mb-0 text-muted small" *ngIf="dataUltimaDeteccao">
              <i class="fe-clock me-1"></i>
              Última análise: {{ dataUltimaDeteccao | date:'dd/MM/yyyy HH:mm' }}
            </p>
          </div>
          <div class="col-md-4 text-end">
            <div class="badge bg-info fs-6 px-3 py-2">
              <i class="fe-cpu me-1"></i>
              IA Ativa
            </div>
          </div>
        </div>

        <div *ngIf="!faseDetectadaPelaIA && mensagensCapturadas.length > 0" class="text-center py-3">
          <p class="text-muted mb-3">
            <i class="fe-message-circle me-2"></i>
            {{ mensagensCapturadas.length }} mensagens capturadas
          </p>
          <button class="btn btn-info" (click)="detectarESalvarFase()" [disabled]="detectandoFase">
            <i class="fe-brain me-2" [class.rotating]="detectandoFase"></i>
            {{ detectandoFase ? 'Analisando conversa...' : 'Detectar Fase da Conversa' }}
          </button>
        </div>

        <div *ngIf="!faseDetectadaPelaIA && mensagensCapturadas.length === 0" class="text-center py-3 text-muted">
          <i class="fe-message-circle fs-2 mb-2 opacity-50"></i>
          <p class="mb-0">Aguardando mensagens para análise automática</p>
        </div>
      </div>
    </div>

    <!-- Card: Assistente IA -->
    <div class="card mb-3">
      <div class="card-header bg-success text-white">
        <h5 class="mb-0">
          <i class="fe-cpu me-2"></i>
          Assistente IA
          <span class="badge bg-light text-success ms-2">Ativo</span>
        </h5>
      </div>
      <div class="card-body">

        <!-- Configuração de Modo -->
        <div class="mb-3">
          <h6 class="text-muted mb-2">Configuração do Assistente:</h6>
          <div class="form-check form-switch d-inline-block me-4">
            <input class="form-check-input" type="checkbox" id="modoRapport"
                   [(ngModel)]="modoRapport" (change)="toggleModoRapport()">
            <label class="form-check-label" for="modoRapport">
              <i class="fe-heart text-danger me-1"></i>
              Rapport/Atratividade
            </label>
          </div>
          <div class="form-check form-switch d-inline-block">
            <input class="form-check-input" type="checkbox" id="modoApresentacao"
                   [(ngModel)]="modoApresentacao" (change)="toggleModoApresentacao()">
            <label class="form-check-label" for="modoApresentacao">
              <i class="fe-user-check text-info me-1"></i>
              Apresentação Profissional
            </label>
          </div>
        </div>

        <!-- Configuração SPIN (modo normal) -->
        <div *ngIf="!modoRapport && !modoApresentacao" class="mb-3">
          <h6 class="text-primary mb-2">
            <i class="fe-target me-1"></i>
            Fase da Venda (SPIN Selling):
          </h6>
          <div class="btn-group d-flex flex-wrap" role="group">
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('auto')"
                    (click)="mudarFaseSpin('auto')">
              <i class="fe-cpu"></i><br>
              <small>Auto</small>
            </button>
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('situacao')"
                    (click)="mudarFaseSpin('situacao')">
              <i class="fe-help-circle"></i><br>
              <small>Situação</small>
            </button>
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('problema')"
                    (click)="mudarFaseSpin('problema')">
              <i class="fe-alert-triangle"></i><br>
              <small>Problema</small>
            </button>
            <button type="button" class="btn flex-fill me-1 mb-1"
                    [ngClass]="getFaseBadgeClass('implicacao')"
                    (click)="mudarFaseSpin('implicacao')">
              <i class="fe-trending-down"></i><br>
              <small>Implicação</small>
            </button>
            <button type="button" class="btn flex-fill mb-1"
                    [ngClass]="getFaseBadgeClass('necessidade')"
                    (click)="mudarFaseSpin('necessidade')">
              <i class="fe-check-circle"></i><br>
              <small>Necessidade</small>
            </button>
          </div>

          <div class="row mt-2">
            <div class="col-md-4">
              <label class="form-label small">Tom da Conversa:</label>
              <select class="form-select form-select-sm" [(ngModel)]="tipoTom">
                <option value="formal">Formal</option>
                <option value="informal">Informal</option>
                <option value="tecnico">Técnico</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Configuração Rapport (modo rapport) -->
        <div *ngIf="modoRapport" class="mb-3">
          <h6 class="text-danger mb-2">
            <i class="fe-heart me-1"></i>
            Tipo de Abordagem Outbound:
          </h6>
          <div class="btn-group d-flex" role="group">
            <button type="button" class="btn flex-fill"
                    [ngClass]="tipoAbordagem === 'direta' ? 'btn-danger' : 'btn-outline-danger'"
                    (click)="mudarTipoAbordagem('direta')">
              <i class="fe-arrow-right"></i> Direta
            </button>
            <button type="button" class="btn flex-fill"
                    [ngClass]="tipoAbordagem === 'indireta' ? 'btn-danger' : 'btn-outline-danger'"
                    (click)="mudarTipoAbordagem('indireta')">
              <i class="fe-smile"></i> Indireta
            </button>
            <button type="button" class="btn flex-fill"
                    [ngClass]="tipoAbordagem === 'consultiva' ? 'btn-danger' : 'btn-outline-danger'"
                    (click)="mudarTipoAbordagem('consultiva')">
              <i class="fe-users"></i> Consultiva
            </button>
          </div>
        </div>

        <!-- Botão de Gerar -->
        <div class="text-center mb-3">
          <button class="btn btn-primary btn-lg px-4 py-2"
                  [disabled]="carregando"
                  (click)="gerarSugestao()">
            <span *ngIf="!carregando && !modoRapport && !modoApresentacao">
              <i class="fe-cpu me-2"></i> Gerar Sugestão de Resposta
            </span>
            <span *ngIf="!carregando && modoRapport">
              <i class="fe-heart me-2"></i> Gerar Mensagem de Rapport
            </span>
            <span *ngIf="!carregando && modoApresentacao">
              <i class="fe-user-check me-2"></i> Gerar Apresentação Profissional
            </span>
            <span *ngIf="carregando">
              <i class="fe-loader rotating me-2"></i>
              {{ carregando && modoRapport ? 'Criando mensagem atrativa...' :
                 carregando && modoApresentacao ? 'Criando apresentação profissional...' :
                 'Analisando conversa...' }}
            </span>
          </button>
        </div>

        <!-- Informações do Modo -->
        <div *ngIf="modoRapport" class="alert alert-info small">
          <i class="fe-info me-2"></i>
          Mensagens otimizadas para trabalho outbound e engajamento inicial
        </div>
        <div *ngIf="modoApresentacao" class="alert alert-success small">
          <i class="fe-info me-2"></i>
          Mensagem de apresentação profissional para primeiro contato
        </div>
      </div>
    </div>

    <!-- Card: Sugestões Geradas -->
    <div *ngIf="sugestoes.length > 0 && !carregando" class="card mb-3">
      <div class="card-header bg-warning text-dark">
        <div class="d-flex align-items-center justify-content-between">
          <h5 class="mb-0">
            <i class="fe-edit-3 me-2"></i>
            Sugestões Geradas
          </h5>
          <div>
            <span class="badge bg-dark text-white me-2">
              {{ sugestoes.length }} opções
            </span>
            <span class="badge bg-success">
              {{ (sugestoes[sugestaoSelecionada]?.confianca * 100) | number:'1.0-0' }}% confiança
            </span>
          </div>
        </div>
      </div>
      <div class="card-body">

        <!-- Área de Texto da Sugestão -->
        <div class="mb-3">
          <label class="form-label fw-bold text-primary">
            <i class="fe-message-square me-1"></i>
            Sugestão {{ sugestaoSelecionada + 1 }} de {{ sugestoes.length }}:
          </label>
          <textarea class="form-control"
                    [(ngModel)]="sugestoes[sugestaoSelecionada].texto"
                    rows="5"
                    placeholder="Sugestão aparecerá aqui..."
                    style="font-size: 15px; line-height: 1.4;"></textarea>
        </div>

        <!-- Botões de Ação -->
        <div class="row g-2 mb-3">
          <div class="col-md-4">
            <button class="btn btn-success w-100" (click)="copiarSugestao(sugestaoSelecionada)">
              <i class="fe-copy me-1"></i>
              Copiar
            </button>
          </div>
          <div class="col-md-4">
            <button class="btn btn-outline-primary w-100" (click)="editarSugestaoIndividual(sugestaoSelecionada)">
              <i class="fe-edit me-1"></i>
              Editar
            </button>
          </div>
          <div class="col-md-4">
            <button class="btn btn-outline-warning w-100" (click)="regenerarSugestao()" [disabled]="carregando">
              <i class="fe-refresh-cw me-1"></i>
              Nova
            </button>
          </div>
        </div>

        <!-- Navegação entre Sugestões -->
        <div *ngIf="sugestoes.length > 1" class="d-flex align-items-center justify-content-center mb-3">
          <button class="btn btn-outline-secondary btn-sm me-2"
                  (click)="selecionarSugestao(sugestaoSelecionada - 1)"
                  [disabled]="sugestaoSelecionada === 0">
            <i class="fe-chevron-left"></i>
          </button>
          <span class="mx-3 fw-bold">
            {{ sugestaoSelecionada + 1 }} de {{ sugestoes.length }}
          </span>
          <button class="btn btn-outline-secondary btn-sm ms-2"
                  (click)="selecionarSugestao(sugestaoSelecionada + 1)"
                  [disabled]="sugestaoSelecionada === sugestoes.length - 1">
            <i class="fe-chevron-right"></i>
          </button>
        </div>

        <!-- Observações da IA -->
        <div *ngIf="faseDetectadaAutomaticamente" class="alert alert-info small">
          <i class="fe-info me-2"></i>
          {{ faseDetectadaAutomaticamente }}
        </div>
      </div>
    </div>

    <!-- Card: Erro -->
    <div *ngIf="erro" class="alert alert-danger">
      <i class="fe-alert-triangle me-2"></i>
      {{ erro }}
    </div>

    <!-- Card: Contexto da Conversa -->
    <div class="card mb-3">
      <div class="card-header bg-info text-white">
        <h5 class="mb-0">
          <i class="fe-message-circle me-2"></i>
          Contexto da Conversa
        </h5>
      </div>
      <div class="card-body">

        <!-- Resumo -->
        <div class="mb-3">
          <h6 class="text-primary mb-2">
            <i class="fe-file-text me-1"></i>
            Resumo da Conversa:
          </h6>
          <p class="text-muted">{{ contextoResumo || 'Aguardando mensagens para análise...' }}</p>
        </div>

        <!-- Mensagens Capturadas -->
        <div class="mb-3">
          <div class="d-flex align-items-center justify-content-between mb-2">
            <h6 class="text-primary mb-0">
              <i class="fe-message-square me-1"></i>
              Mensagens Capturadas:
            </h6>
            <span class="badge bg-secondary">{{ mensagensCapturadas.length }} mensagens</span>
          </div>

          <div *ngIf="capturandoMensagens" class="alert alert-info">
            <i class="fe-loader rotating me-2"></i>
            Aguardando mensagens do WhatsApp...
          </div>

          <div *ngIf="!capturandoMensagens && mensagensCapturadas.length === 0"
               class="alert alert-warning">
            <i class="fe-alert-circle me-2"></i>
            Nenhuma mensagem capturada ainda
          </div>

          <div *ngIf="mensagensCapturadas.length > 0"
               class="border rounded p-3 bg-light"
               style="max-height: 250px; overflow-y: auto;">
            <div *ngFor="let msg of mensagensCapturadas"
                 class="mb-2 p-2 rounded"
                 [ngClass]="{'bg-white border-start border-primary border-3': msg.remetente === 'Lead',
                            'bg-primary text-white ms-3': msg.remetente === 'Eu'}">
              <small class="d-block fw-bold">
                {{ msg.remetente }} - {{ msg.horario }}
              </small>
              <p class="mb-0 small">{{ msg.texto }}</p>
            </div>
          </div>
        </div>

        <!-- Botões de Ação -->
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary btn-sm"
                  (click)="simularMensagens()"
                  [disabled]="!capturandoMensagens">
            <i class="fe-play me-1"></i>
            Simular Mensagens
          </button>
          <button class="btn btn-outline-secondary btn-sm"
                  (click)="limparMensagens()"
                  [disabled]="mensagensCapturadas.length === 0">
            <i class="fe-trash-2 me-1"></i>
            Limpar
          </button>
        </div>
      </div>
    </div>

    <!-- Card: Captura Rápida (só para novos leads) -->
    <div *ngIf="!contato.id" class="collapse" id="capturaRapida">
      <div class="card mb-3">
        <div class="card-header bg-secondary text-white">
          <h5 class="mb-0">
            <i class="fe-user-plus me-2"></i>
            Cadastrar Novo Lead
          </h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label">Nome do contato:</label>
              <input type="text" class="form-control"
                     [value]="nomeWhatsApp" readonly
                     style="background-color: #f8f9fa;">
            </div>
            <div class="col-md-6">
              <label class="form-label">Nome da empresa:</label>
              <input type="text" class="form-control"
                     placeholder="Ex: Restaurante do João"
                     [(ngModel)]="novoLead.empresa">
            </div>
            <div class="col-md-6">
              <label class="form-label">Email:</label>
              <input type="email" class="form-control"
                     placeholder="<EMAIL>"
                     [(ngModel)]="novoLead.email">
            </div>
            <div class="col-md-6">
              <label class="form-label">Telefone:</label>
              <input type="text" class="form-control"
                     [value]="contato.telefone" readonly
                     style="background-color: #f8f9fa;">
            </div>
            <div class="col-12">
              <label class="form-label">Observações:</label>
              <textarea class="form-control" rows="3"
                        placeholder="Ex: Interessado em cardápio digital, tem 2 lojas..."
                        [(ngModel)]="novoLead.observacoes"></textarea>
            </div>
            <div class="col-12 text-end">
              <button class="btn btn-success" (click)="cadastrarLeadRapido()">
                <i class="fe-save me-2"></i>
                Salvar e Cadastrar Lead
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
